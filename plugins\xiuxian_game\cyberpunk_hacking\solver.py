"""
黑客破译游戏的求解器
基于赛博朋克2077的算法实现
"""

from typing import List, Optional, Set, Tuple, Dict
from collections import deque
import copy
try:
    from .core import Matrix, Sequence, Solution, Coordinate, Direction, SearchState
except ImportError:
    from core import Matrix, Sequence, Solution, Coordinate, Direction, SearchState


class HackingSolver:
    """黑客破译求解器"""
    
    def __init__(self, matrix: Matrix, sequences: List[Sequence], buffer_size: int):
        self.matrix = matrix
        self.sequences = sequences
        self.buffer_size = buffer_size
        self.solutions: List[Solution] = []
    
    def find_best_solution(self) -> Optional[Solution]:
        """找到最佳解决方案"""
        self.solutions = []
        
        # 为每个序列尝试找到解决方案
        for sequence_combinations in self._generate_sequence_combinations():
            solutions = self._solve_for_sequences(sequence_combinations)
            self.solutions.extend(solutions)
        
        if not self.solutions:
            return None
        
        # 选择最佳解决方案
        return self._select_best_solution()
    
    def _generate_sequence_combinations(self) -> List[List[int]]:
        """生成序列组合，优先考虑更多序列的组合"""
        combinations = []
        
        # 尝试所有序列
        combinations.append(list(range(len(self.sequences))))
        
        # 尝试单个序列
        for i in range(len(self.sequences)):
            combinations.append([i])
        
        # 尝试两个序列的组合
        for i in range(len(self.sequences)):
            for j in range(i + 1, len(self.sequences)):
                combinations.append([i, j])
        
        return combinations
    
    def _solve_for_sequences(self, sequence_indices: List[int]) -> List[Solution]:
        """为指定的序列组合寻找解决方案"""
        target_sequences = [self.sequences[i] for i in sequence_indices]
        
        # 使用广度优先搜索找到所有可能的路径
        solutions = []
        
        # 从每个位置开始尝试
        for start_y in range(self.matrix.height):
            for start_x in range(self.matrix.width):
                start_coord = Coordinate(start_x, start_y)
                paths = self._find_paths_from_start(start_coord, target_sequences)
                
                for path in paths:
                    matched_sequences = self._get_matched_sequences(path, sequence_indices)
                    if matched_sequences:
                        total_reward = sum(self.sequences[i].reward for i in matched_sequences)
                        solution = Solution(
                            path=path,
                            matched_sequences=matched_sequences,
                            total_reward=total_reward,
                            path_length=len(path)
                        )
                        solutions.append(solution)
        
        return solutions
    
    def _find_paths_from_start(self, start: Coordinate, target_sequences: List[Sequence]) -> List[List[Coordinate]]:
        """从指定起始位置寻找路径"""
        paths = []
        
        # 初始化搜索状态
        initial_used = [[False for _ in range(self.matrix.width)] for _ in range(self.matrix.height)]
        initial_state = SearchState(
            pattern_index=0,
            used_positions=initial_used,
            path=[start],
            allowed_direction=Direction.HORIZONTAL,
            current_pos=start
        )
        
        queue = deque([initial_state])
        
        while queue:
            state = queue.popleft()
            
            # 检查路径长度限制
            if len(state.path) > self.buffer_size:
                continue
            
            # 检查是否匹配了任何序列
            if self._path_matches_any_sequence(state.path, target_sequences):
                paths.append(state.path.copy())
            
            # 如果已经达到缓冲区大小，不再扩展
            if len(state.path) >= self.buffer_size:
                continue
            
            # 扩展搜索
            next_states = self._get_next_states(state)
            queue.extend(next_states)
        
        return paths
    
    def _get_next_states(self, state: SearchState) -> List[SearchState]:
        """获取下一步的搜索状态"""
        next_states = []
        current_pos = state.current_pos
        
        # 标记当前位置为已使用
        new_used = copy.deepcopy(state.used_positions)
        new_used[current_pos.y][current_pos.x] = True
        
        if state.allowed_direction == Direction.HORIZONTAL:
            # 水平移动：同一行的其他列
            for x in range(self.matrix.width):
                if x != current_pos.x and not new_used[current_pos.y][x]:
                    next_coord = Coordinate(x, current_pos.y)
                    next_state = SearchState(
                        pattern_index=state.pattern_index,
                        used_positions=copy.deepcopy(new_used),
                        path=state.path + [next_coord],
                        allowed_direction=Direction.VERTICAL,
                        current_pos=next_coord
                    )
                    next_states.append(next_state)
        else:
            # 垂直移动：同一列的其他行
            for y in range(self.matrix.height):
                if y != current_pos.y and not new_used[y][current_pos.x]:
                    next_coord = Coordinate(current_pos.x, y)
                    next_state = SearchState(
                        pattern_index=state.pattern_index,
                        used_positions=copy.deepcopy(new_used),
                        path=state.path + [next_coord],
                        allowed_direction=Direction.HORIZONTAL,
                        current_pos=next_coord
                    )
                    next_states.append(next_state)
        
        return next_states
    
    def _path_matches_any_sequence(self, path: List[Coordinate], sequences: List[Sequence]) -> bool:
        """检查路径是否匹配任何序列"""
        if len(path) < 2:
            return False
        
        path_values = [self.matrix.get_value(coord) for coord in path]
        
        for sequence in sequences:
            if self._sequence_matches_path(sequence.values, path_values):
                return True
        
        return False
    
    def _sequence_matches_path(self, sequence: List[str], path_values: List[str]) -> bool:
        """检查序列是否在路径中匹配"""
        if len(sequence) > len(path_values):
            return False
        
        # 查找序列在路径中的连续匹配
        for i in range(len(path_values) - len(sequence) + 1):
            if path_values[i:i + len(sequence)] == sequence:
                return True
        
        return False
    
    def _get_matched_sequences(self, path: List[Coordinate], sequence_indices: List[int]) -> List[int]:
        """获取路径匹配的序列索引"""
        matched = []
        path_values = [self.matrix.get_value(coord) for coord in path]
        
        for idx in sequence_indices:
            sequence = self.sequences[idx]
            if self._sequence_matches_path(sequence.values, path_values):
                matched.append(idx)
        
        return matched
    
    def _select_best_solution(self) -> Solution:
        """选择最佳解决方案"""
        if not self.solutions:
            return None
        
        # 按以下优先级排序：
        # 1. 匹配的序列数量（越多越好）
        # 2. 总奖励（越高越好）
        # 3. 路径长度（越短越好）
        
        def solution_score(solution: Solution) -> Tuple[int, int, int]:
            return (
                len(solution.matched_sequences),  # 匹配序列数量
                solution.total_reward,            # 总奖励
                -solution.path_length             # 路径长度（负数，越短越好）
            )
        
        self.solutions.sort(key=solution_score, reverse=True)
        return self.solutions[0]
    
    def get_all_solutions(self) -> List[Solution]:
        """获取所有找到的解决方案"""
        return self.solutions.copy()


class OptimizedSolver(HackingSolver):
    """优化的求解器，使用更高效的算法"""
    
    def __init__(self, matrix: Matrix, sequences: List[Sequence], buffer_size: int):
        super().__init__(matrix, sequences, buffer_size)
        self.memo: Dict[str, List[List[Coordinate]]] = {}
    
    def _find_paths_from_start(self, start: Coordinate, target_sequences: List[Sequence]) -> List[List[Coordinate]]:
        """使用记忆化的路径搜索"""
        # 创建缓存键
        sequences_key = "_".join("_".join(seq.values) for seq in target_sequences)
        cache_key = f"{start.x}_{start.y}_{sequences_key}"
        
        if cache_key in self.memo:
            return self.memo[cache_key]
        
        paths = super()._find_paths_from_start(start, target_sequences)
        self.memo[cache_key] = paths
        return paths
    
    def find_best_solution(self) -> Optional[Solution]:
        """优化的最佳解决方案搜索"""
        self.memo.clear()
        return super().find_best_solution()
