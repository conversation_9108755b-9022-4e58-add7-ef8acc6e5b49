"""
黑客破译游戏的命令接口
"""

from nonebot import on_command, on_regex
from nonebot.adapters.qq import MessageEvent, Message
from nonebot.params import CommandArg
import re
from typing import Dict, Optional
from .core import HackingGame, GameConfig, Coordinate
from .generator import AdvancedPuzzleGenerator


# 存储用户的游戏实例
user_games: Dict[str, HackingGame] = {}


# 开始黑客破译游戏
hack_start = on_command("黑客破译", aliases={"开始黑客破译", "breach"}, priority=5, block=True)

@hack_start.handle()
async def handle_hack_start(event: MessageEvent, args: Message = CommandArg()):
    user_id = event.get_user_id()
    
    # 解析难度参数
    difficulty = 1
    args_text = args.extract_plain_text().strip()
    if args_text:
        try:
            difficulty = int(args_text)
            difficulty = max(1, min(5, difficulty))
        except ValueError:
            pass
    
    # 创建游戏配置
    config = GameConfig()
    
    # 创建新游戏
    game = HackingGame(config)
    
    # 设置高级生成器
    generator = AdvancedPuzzleGenerator(config)
    generator.set_difficulty(difficulty)
    
    # 生成谜题
    if game.generate_new_puzzle():
        user_games[user_id] = game
        
        # 显示游戏界面
        game_display = format_game_display(game)
        
        await hack_start.send(
            f"🔓 黑客破译游戏开始！\n"
            f"难度等级: {difficulty}/5\n"
            f"缓冲区大小: {config.buffer_size}\n\n"
            f"{game_display}\n\n"
            f"💡 使用 '破译 坐标序列' 来提交解答\n"
            f"例如: 破译 (0,0) (1,0) (1,1) (2,1)\n"
            f"使用 '破译提示' 查看解决方案\n"
            f"使用 '破译规则' 查看游戏规则"
        )
    else:
        await hack_start.send("❌ 生成谜题失败，请重试")


# 提交解答
hack_solve = on_regex(r"^破译\s+(.+)", priority=5, block=True)

@hack_solve.handle()
async def handle_hack_solve(event: MessageEvent):
    user_id = event.get_user_id()
    if user_id not in user_games:
        await hack_solve.send("❌ 请先使用 '黑客破译' 开始游戏")
        return
    
    game = user_games[user_id]
    
    # 提取坐标
    message_text = str(event.get_message()).strip()
    match = re.match(r"^破译\s+(.+)", message_text)
    if not match:
        await hack_solve.send("❌ 格式错误，请使用: 破译 (x,y) (x,y) ...")
        return
    
    coords_text = match.group(1)
    
    try:
        coordinates = parse_coordinates(coords_text)
        if not coordinates:
            await hack_solve.send("❌ 未找到有效坐标，格式: (x,y) (x,y) ...")
            return
        
        # 验证解答
        is_valid, matched_sequences, total_reward = game.validate_player_solution(coordinates)
        
        if is_valid:
            # 计算经验和奖励
            exp_reward = total_reward // 10
            money_reward = total_reward
            
            # 这里可以集成到修仙系统中
            # await add_player_exp(user_id, exp_reward)
            # await add_player_money(user_id, money_reward)
            
            sequence_names = [game.sequences[i].name for i in matched_sequences]
            
            await hack_solve.send(
                f"🎉 破译成功！\n"
                f"匹配序列: {', '.join(sequence_names)}\n"
                f"获得奖励: {total_reward} 点数\n"
                f"获得经验: {exp_reward} 点\n"
                f"路径长度: {len(coordinates)}/{game.config.buffer_size}\n\n"
                f"路径: {format_path(coordinates, game)}"
            )
            
            # 清除游戏
            del user_games[user_id]
        else:
            await hack_solve.send(
                f"❌ 破译失败！\n"
                f"路径无效或未匹配任何序列\n"
                f"请检查路径是否遵循游戏规则"
            )
    
    except Exception as e:
        await hack_solve.send(f"❌ 解析坐标时出错: {str(e)}")


# 查看提示
hack_hint = on_command("破译提示", aliases={"黑客提示"}, priority=5, block=True)

@hack_hint.handle()
async def handle_hack_hint(event: MessageEvent):
    user_id = event.get_user_id()
    
    if user_id not in user_games:
        await hack_hint.send("❌ 请先使用 '黑客破译' 开始游戏")
        return
    
    game = user_games[user_id]
    
    # 获取解决方案
    solution = game.solve_puzzle()
    
    if solution:
        sequence_names = [game.sequences[i].name for i in solution.matched_sequences]
        
        await hack_hint.send(
            f"💡 解决方案提示:\n"
            f"最佳路径: {format_path(solution.path, game)}\n"
            f"匹配序列: {', '.join(sequence_names)}\n"
            f"总奖励: {solution.total_reward}\n"
            f"路径长度: {solution.path_length}/{game.config.buffer_size}"
        )
    else:
        await hack_hint.send("❌ 未找到解决方案，这个谜题可能无解")


# 查看游戏规则
hack_rules = on_command("破译规则", aliases={"黑客规则"}, priority=5, block=True)

@hack_rules.handle()
async def handle_hack_rules(event: MessageEvent):
    rules_text = """
🔓 黑客破译游戏规则

📋 游戏目标:
在矩阵中找到路径，按顺序匹配目标序列

🎯 移动规则:
1. 第一步必须在同一行内移动（水平）
2. 之后必须交替在行和列之间移动
3. 不能重复访问同一个位置
4. 路径长度不能超过缓冲区大小

💎 评分系统:
- 匹配更多序列获得更高分数
- 较短的路径获得额外奖励
- 不同序列有不同的奖励值

📝 输入格式:
破译 (列,行) (列,行) (列,行) ...
例如: 破译 (0,0) (1,0) (1,1) (2,1)

🎮 命令列表:
- 黑客破译 [难度1-5] - 开始游戏
- 破译 坐标序列 - 提交解答
- 破译提示 - 查看解决方案
- 破译规则 - 查看此帮助
"""
    await hack_rules.send(rules_text.strip())


def parse_coordinates(coords_text: str) -> list[Coordinate]:
    """解析坐标字符串"""
    coordinates = []
    
    # 匹配 (x,y) 格式的坐标
    pattern = r'\((\d+),(\d+)\)'
    matches = re.findall(pattern, coords_text)
    
    for match in matches:
        x, y = int(match[0]), int(match[1])
        coordinates.append(Coordinate(x, y))
    
    return coordinates


def format_game_display(game: HackingGame) -> str:
    """格式化游戏显示"""
    if not game.matrix or not game.sequences:
        return "游戏数据无效"
    
    # 显示矩阵
    matrix_display = "🔢 代码矩阵:\n"
    matrix_display += "   " + " ".join(f"{i:>2}" for i in range(game.matrix.width)) + "\n"
    
    for y, row in enumerate(game.matrix.grid):
        matrix_display += f"{y:>2} " + " ".join(f"{val:>2}" for val in row) + "\n"
    
    # 显示目标序列
    sequences_display = "\n🎯 目标序列:\n"
    for i, sequence in enumerate(game.sequences):
        sequences_display += f"{i+1}. {sequence.name}: {' → '.join(sequence.values)} (奖励: {sequence.reward})\n"
    
    return matrix_display + sequences_display


def format_path(path: list[Coordinate], game: HackingGame) -> str:
    """格式化路径显示"""
    if not path:
        return "空路径"
    
    path_coords = " → ".join(f"({coord.x},{coord.y})" for coord in path)
    path_values = " → ".join(game.matrix.get_value(coord) for coord in path)
    
    return f"坐标: {path_coords}\n值: {path_values}"
